/**
 * 本地数据管理器
 * 直接从本地文件获取地图数据，避免API请求
 */

import type { DaycareGeoJSON, DaycareStats } from "@/types/daycare";

// 直接导入数据文件
import zipBoundariesData from "./california-zip-codes.geojson";
import daycareGeoJsonData from "./daycare_data_2023.geojson";
import daycarePropertiesData from "./daycare_properties_2023.json";
import daycareStatsData from "./processed_2023/stats_2023.json";

/**
 * 本地数据缓存
 */
class LocalDataCache {
  private zipBoundariesData: any = null;
  private daycareGeoJsonData: DaycareGeoJSON | null = null;
  private daycarePropertiesData: any = null;
  private daycareStatsData: DaycareStats | null = null;
  private isInitialized = false;

  /**
   * 初始化本地数据
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log("🔄 初始化本地数据管理器...");

      // 同步加载所有导入的数据
      this.loadZipBoundaries();
      this.loadDaycareGeoJson();
      this.loadDaycareProperties();
      this.loadDaycareStats();

      this.isInitialized = true;
      console.log("✅ 本地数据管理器初始化完成");
    } catch (error) {
      console.error("❌ 本地数据管理器初始化失败:", error);
      throw error;
    }
  }

  /**
   * 加载ZIP边界数据
   */
  private loadZipBoundaries(): void {
    try {
      // 直接使用导入的数据
      this.zipBoundariesData = zipBoundariesData;
      console.log(
        "✅ ZIP边界数据已加载:",
        this.zipBoundariesData.features?.length || 0,
        "个区域"
      );
    } catch (error) {
      console.warn("⚠️ ZIP边界数据加载失败，使用示例数据:", error);
      this.zipBoundariesData = this.createSampleZipBoundaries();
    }
  }

  /**
   * 加载托儿所GeoJSON数据
   */
  private loadDaycareGeoJson(): void {
    try {
      // 直接使用导入的数据
      this.daycareGeoJsonData = daycareGeoJsonData as DaycareGeoJSON;
      console.log(
        "✅ 托儿所GeoJSON数据已加载:",
        this.daycareGeoJsonData?.features?.length || 0,
        "个点"
      );
    } catch (error) {
      console.warn("⚠️ 托儿所GeoJSON数据加载失败，使用示例数据:", error);
      this.daycareGeoJsonData = this.createSampleDaycareGeoJson();
    }
  }

  /**
   * 加载托儿所属性数据
   */
  private loadDaycareProperties(): void {
    try {
      // 直接使用导入的数据
      const data = daycarePropertiesData as any;
      this.daycarePropertiesData = data.properties || data;
      console.log(
        "✅ 托儿所属性数据已加载:",
        Object.keys(this.daycarePropertiesData).length,
        "个ZIP区域"
      );
    } catch (error) {
      console.warn("⚠️ 托儿所属性数据加载失败，使用示例数据:", error);
      this.daycarePropertiesData = this.createSampleProperties();
    }
  }

  /**
   * 加载托儿所统计数据
   */
  private loadDaycareStats(): void {
    try {
      // 直接使用导入的数据
      this.daycareStatsData = daycareStatsData as DaycareStats;
      console.log("✅ 托儿所统计数据已加载");
    } catch (error) {
      console.warn("⚠️ 托儿所统计数据加载失败，使用示例数据:", error);
      this.daycareStatsData = this.createSampleStats();
    }
  }

  /**
   * 获取ZIP边界数据
   */
  getZipBoundaries(zipCode?: string): any {
    if (!this.zipBoundariesData) {
      return null;
    }

    if (zipCode) {
      const filteredFeatures =
        this.zipBoundariesData.features?.filter((feature: any) => {
          const featureZip =
            feature.properties?.ZCTA5CE10 ||
            feature.properties?.zipCode ||
            feature.properties?.zip_code;
          return featureZip === zipCode;
        }) || [];

      return {
        type: "FeatureCollection",
        features: filteredFeatures,
      };
    }

    return this.zipBoundariesData;
  }

  /**
   * 获取托儿所GeoJSON数据
   */
  getDaycareGeoJson(zipCode?: string): DaycareGeoJSON | null {
    if (!this.daycareGeoJsonData) {
      return null;
    }

    if (zipCode) {
      const filteredFeatures = this.daycareGeoJsonData.features.filter(
        (feature) => feature.properties.zip_code === zipCode
      );

      return {
        ...this.daycareGeoJsonData,
        features: filteredFeatures,
        metadata: {
          ...this.daycareGeoJsonData.metadata,
          total_features: filteredFeatures.length,
        },
      };
    }

    return this.daycareGeoJsonData;
  }

  /**
   * 获取托儿所属性数据
   */
  getDaycareProperties(zipCode?: string): any {
    if (!this.daycarePropertiesData) {
      return null;
    }

    if (zipCode) {
      return this.daycarePropertiesData[zipCode] || null;
    }

    return this.daycarePropertiesData;
  }

  /**
   * 获取托儿所统计数据
   */
  getDaycareStats(): DaycareStats | null {
    return this.daycareStatsData;
  }

  /**
   * 检查数据是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 创建示例ZIP边界数据
   */
  private createSampleZipBoundaries(): any {
    return {
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          properties: {
            ZCTA5CE10: "90210",
            zipCode: "90210",
            name: "Beverly Hills",
          },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [-118.4, 34.08],
                [-118.38, 34.08],
                [-118.38, 34.1],
                [-118.4, 34.1],
                [-118.4, 34.08],
              ],
            ],
          },
        },
        {
          type: "Feature",
          properties: {
            ZCTA5CE10: "90211",
            zipCode: "90211",
            name: "Beverly Hills East",
          },
          geometry: {
            type: "Polygon",
            coordinates: [
              [
                [-118.38, 34.08],
                [-118.36, 34.08],
                [-118.36, 34.1],
                [-118.38, 34.1],
                [-118.38, 34.08],
              ],
            ],
          },
        },
      ],
    };
  }

  /**
   * 创建示例托儿所GeoJSON数据
   */
  private createSampleDaycareGeoJson(): DaycareGeoJSON {
    return {
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          properties: {
            id: "sample-1",
            name: "示例托儿所 1",
            zip_code: "90210",
            saturation_level: "medium",
            capacity: 50,
            enrollment: 35,
          },
          geometry: {
            type: "Point",
            coordinates: [-118.39, 34.09],
          },
        },
        {
          type: "Feature",
          properties: {
            id: "sample-2",
            name: "示例托儿所 2",
            zip_code: "90211",
            saturation_level: "high",
            capacity: 40,
            enrollment: 38,
          },
          geometry: {
            type: "Point",
            coordinates: [-118.37, 34.09],
          },
        },
      ],
      metadata: {
        total_features: 2,
        data_source: "sample",
        last_updated: new Date().toISOString(),
      },
    };
  }

  /**
   * 创建示例属性数据
   */
  private createSampleProperties(): any {
    return {
      "90210": {
        zip_code: "90210",
        total_capacity: 200,
        total_enrollment: 150,
        saturation: 0.75,
        saturation_level: "high",
        daycare_count: 4,
      },
      "90211": {
        zip_code: "90211",
        total_capacity: 150,
        total_enrollment: 90,
        saturation: 0.6,
        saturation_level: "medium",
        daycare_count: 3,
      },
    };
  }

  /**
   * 创建示例统计数据
   */
  private createSampleStats(): DaycareStats {
    return {
      year: 2023,
      total_zip_codes: 2,
      saturation: {
        min: 0.3,
        max: 0.9,
        avg: 0.686,
      },
      births: {
        min: 50,
        max: 200,
        avg: 125,
        total: 500,
      },
      capacity: {
        min: 20,
        max: 100,
        avg: 50,
        total: 350,
      },
      level_distribution: {
        low: 1,
        medium: 3,
        high: 2,
        very_high: 1,
      },
      generated_at: new Date().toISOString(),
    };
  }
}

/**
 * 全局本地数据管理器实例
 */
export const localDataManager = new LocalDataCache();

/**
 * 本地数据加载器
 * 替代原有的API请求逻辑
 */
export class LocalDataLoader {
  /**
   * 预加载ZIP边界数据
   */
  async preloadZipBoundaries(): Promise<any> {
    await localDataManager.initialize();
    return localDataManager.getZipBoundaries();
  }

  /**
   * 加载托儿所数据
   */
  async loadDaycareData(zipData?: any): Promise<any> {
    await localDataManager.initialize();

    const zipBoundaries = zipData || localDataManager.getZipBoundaries();
    const properties = localDataManager.getDaycareProperties();

    if (zipBoundaries && properties) {
      // 合并ZIP边界和属性数据
      const mergedData = this.mergeZipBoundariesWithProperties(
        zipBoundaries,
        properties
      );
      if (mergedData) {
        return { type: "zip-areas", data: mergedData };
      }
    }

    // 回退到点数据
    const geoJsonData = localDataManager.getDaycareGeoJson();
    if (geoJsonData) {
      return { type: "points", data: geoJsonData };
    }

    throw new Error("无法加载托儿所数据");
  }

  /**
   * 加载点数据
   */
  async loadPointData(): Promise<{ type: string; data: DaycareGeoJSON }> {
    await localDataManager.initialize();

    const data = localDataManager.getDaycareGeoJson();
    if (!data) {
      throw new Error("点数据加载失败");
    }

    return { type: "points", data };
  }

  /**
   * 获取特定ZIP码数据
   */
  async getZipData(zipCode: string): Promise<any> {
    await localDataManager.initialize();
    return localDataManager.getDaycareProperties(zipCode);
  }

  /**
   * 获取统计数据
   */
  async getStats(): Promise<DaycareStats | null> {
    await localDataManager.initialize();
    return localDataManager.getDaycareStats();
  }

  /**
   * 合并ZIP边界和属性数据
   */
  private mergeZipBoundariesWithProperties(
    zipBoundaries: any,
    properties: any
  ): any {
    if (!zipBoundaries?.features || !properties) {
      return null;
    }

    const mergedFeatures = zipBoundaries.features
      .map((feature: any) => {
        const zipCode =
          feature.properties?.ZCTA5CE10 ||
          feature.properties?.zipCode ||
          feature.properties?.zip_code;

        const zipProperties = properties[zipCode];

        return {
          ...feature,
          properties: {
            ...feature.properties,
            ...zipProperties,
          },
        };
      })
      .filter((feature: any) => feature.properties.saturation !== undefined);

    return {
      type: "FeatureCollection",
      features: mergedFeatures,
    };
  }
}

// 在开发环境中导出到全局对象
if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
  (window as any).localDataManager = localDataManager;
  console.log("🔧 本地数据管理器已加载到 window.localDataManager");
}
