declare namespace NodeJS {
  interface ProcessEnv {
    // Next.js 环境变量
    NODE_ENV: "development" | "production" | "test";
    NEXT_PUBLIC_APP_URL: string;

    // API 相关
    NEXT_PUBLIC_API_URL?: string;
    NEXT_PUBLIC_API_VERSION?: string;

    // 功能开关
    NEXT_PUBLIC_ENABLE_MOCK?: string;
    NEXT_PUBLIC_ENABLE_DEBUG?: string;
    NEXT_PUBLIC_LOG_LEVEL?: "debug" | "info" | "warn" | "error";

    // 第三方服务
    NEXT_PUBLIC_ANALYTICS_ID?: string;
    NEXT_PUBLIC_SENTRY_DSN?: string;

    // 开发工具
    ANALYZE?: string;
    BUNDLE_ANALYZE?: string;
  }
}

// 扩展 Window 对象类型
declare interface Window {
  // 全局分析工具
  gtag?: (...args: unknown[]) => void;
  dataLayer?: unknown[];

  // 开发工具
  __REDUX_DEVTOOLS_EXTENSION__?: unknown;
  __NEXT_DATA__?: {
    props: unknown;
    page: string;
    query: Record<string, unknown>;
    buildId: string;
  };
}

// 模块声明
declare module "*.svg" {
  const content: React.FunctionComponent<React.SVGAttributes<SVGElement>>;
  export default content;
}

declare module "*.png" {
  const content: string;
  export default content;
}

declare module "*.jpg" {
  const content: string;
  export default content;
}

declare module "*.jpeg" {
  const content: string;
  export default content;
}

declare module "*.gif" {
  const content: string;
  export default content;
}

declare module "*.webp" {
  const content: string;
  export default content;
}

declare module "*.ico" {
  const content: string;
  export default content;
}

declare module "*.bmp" {
  const content: string;
  export default content;
}

// CSS 模块声明
declare module "*.module.css" {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module "*.module.scss" {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module "*.module.sass" {
  const classes: { readonly [key: string]: string };
  export default classes;
}

// JSON 模块声明
declare module "*.json" {
  const value: unknown;
  export default value;
}

// GeoJSON 模块声明
declare module "*.geojson" {
  const value: unknown;
  export default value;
}
